<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试添加用户API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #8B5CF6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #7C3AED;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试添加用户API</h1>
        <form id="addUserForm">
            <div class="form-group">
                <label for="phone">手机号:</label>
                <input type="text" id="phone" name="phone" value="13800138888" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" value="123456" required>
            </div>
            
            <div class="form-group">
                <label for="nickname">昵称:</label>
                <input type="text" id="nickname" name="nickname" value="测试用户">
            </div>
            
            <div class="form-group">
                <label for="memberLevel">会员等级:</label>
                <select id="memberLevel" name="memberLevel">
                    <option value="0">普通会员</option>
                    <option value="1">黄金会员</option>
                    <option value="2">铂金会员</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="status">状态:</label>
                <select id="status" name="status">
                    <option value="1">启用</option>
                    <option value="0">禁用</option>
                </select>
            </div>
            
            <button type="submit">添加用户</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('addUserForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                phone: formData.get('phone'),
                password: formData.get('password'),
                nickname: formData.get('nickname'),
                memberLevel: parseInt(formData.get('memberLevel')),
                status: parseInt(formData.get('status'))
            };
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在添加用户...';
            
            try {
                const response = await fetch('http://localhost:8081/manager/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer your-token-here' // 需要替换为实际的token
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok && result.code === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '添加用户成功！\n\n' + JSON.stringify(result, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '添加用户失败：\n\n' + JSON.stringify(result, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '请求失败：' + error.message;
            }
        });
    </script>
</body>
</html>
