<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试编辑用户API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #5a67d8;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试编辑用户API</h1>
        
        <div class="note">
            <strong>注意：</strong>请先获取一个现有用户的信息，然后修改需要更新的字段。密码字段留空则不修改密码。
        </div>
        
        <button onclick="getUserList()">获取用户列表</button>
        <button onclick="clearForm()">清空表单</button>
        
        <form id="editUserForm">
            <div class="form-group">
                <label for="userId">用户ID:</label>
                <input type="text" id="userId" name="userId" placeholder="请先获取用户列表选择用户" required readonly>
            </div>
            
            <div class="form-group">
                <label for="phone">手机号:</label>
                <input type="text" id="phone" name="phone" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码 (留空则不修改):</label>
                <input type="password" id="password" name="password" placeholder="留空则不修改密码">
            </div>
            
            <div class="form-group">
                <label for="nickname">昵称:</label>
                <input type="text" id="nickname" name="nickname" required>
            </div>
            
            <div class="form-group">
                <label for="memberLevel">会员等级:</label>
                <select id="memberLevel" name="memberLevel">
                    <option value="0">普通会员</option>
                    <option value="1">黄金会员</option>
                    <option value="2">铂金会员</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="status">状态:</label>
                <select id="status" name="status">
                    <option value="1">启用</option>
                    <option value="0">禁用</option>
                </select>
            </div>
            
            <button type="submit">更新用户</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        // 获取用户列表
        async function getUserList() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在获取用户列表...';
            
            try {
                const response = await fetch('http://localhost:8081/manager/users?current=1&size=10', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer your-token-here' // 需要替换为实际的token
                    }
                });
                
                const result = await response.json();
                
                if (response.ok && result.code === 200) {
                    resultDiv.className = 'result success';
                    let userListHtml = '用户列表：\n\n';
                    result.data.records.forEach(user => {
                        userListHtml += `ID: ${user.userId}\n`;
                        userListHtml += `手机号: ${user.phone}\n`;
                        userListHtml += `昵称: ${user.nickname}\n`;
                        userListHtml += `会员等级: ${user.memberLevelName}\n`;
                        userListHtml += `状态: ${user.statusName}\n`;
                        userListHtml += `点击填充表单: `;
                        userListHtml += `<button onclick="fillForm('${user.userId}', '${user.phone}', '${user.nickname}', ${user.memberLevel}, ${user.status})">选择此用户</button>\n\n`;
                    });
                    resultDiv.innerHTML = userListHtml;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '获取用户列表失败：\n\n' + JSON.stringify(result, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '请求失败：' + error.message;
            }
        }
        
        // 填充表单
        function fillForm(userId, phone, nickname, memberLevel, status) {
            document.getElementById('userId').value = userId;
            document.getElementById('phone').value = phone;
            document.getElementById('nickname').value = nickname;
            document.getElementById('memberLevel').value = memberLevel;
            document.getElementById('status').value = status;
            document.getElementById('password').value = ''; // 密码留空
        }
        
        // 清空表单
        function clearForm() {
            document.getElementById('editUserForm').reset();
            document.getElementById('result').innerHTML = '';
        }
        
        // 提交编辑用户表单
        document.getElementById('editUserForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const userId = formData.get('userId');
            const data = {
                userId: userId,
                phone: formData.get('phone'),
                nickname: formData.get('nickname'),
                memberLevel: parseInt(formData.get('memberLevel')),
                status: parseInt(formData.get('status'))
            };
            
            // 只有在密码不为空时才包含密码
            const password = formData.get('password');
            if (password && password.trim()) {
                data.password = password;
            }
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在更新用户...';
            
            try {
                const response = await fetch(`http://localhost:8081/manager/users/${userId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer your-token-here' // 需要替换为实际的token
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok && result.code === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '更新用户成功！\n\n' + JSON.stringify(result, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '更新用户失败：\n\n' + JSON.stringify(result, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '请求失败：' + error.message;
            }
        });
    </script>
</body>
</html>
