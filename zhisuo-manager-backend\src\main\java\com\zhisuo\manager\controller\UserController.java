package com.zhisuo.manager.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhisuo.manager.common.Result;
import com.zhisuo.manager.dto.UserQueryRequest;
import com.zhisuo.manager.dto.UserStatusRequest;
import com.zhisuo.manager.service.UserService;
import com.zhisuo.manager.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/users")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 分页查询用户列表
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public Result<Map<String, Object>> getUserPage(UserQueryRequest request) {
        try {
            IPage<UserVO> page = userService.getUserPage(request);

            // 构建返回数据结构
            Map<String, Object> result = new HashMap<>();
            result.put("records", page.getRecords());
            result.put("total", page.getTotal());
            result.put("current", page.getCurrent());
            result.put("size", page.getSize());
            result.put("pages", page.getPages());

            return Result.success(result);
        } catch (Exception e) {
            log.error("查询用户列表失败", e);
            return Result.error("查询用户列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户详情
     */
    @GetMapping("/{userId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public Result<UserVO> getUserDetail(@PathVariable String userId) {
        try {
            UserVO userVO = userService.getUserDetail(userId);
            return Result.success(userVO);
        } catch (Exception e) {
            log.error("获取用户详情失败", e);
            return Result.error("获取用户详情失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新用户状态
     */
    @PutMapping("/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public Result<Void> updateUserStatus(@Valid @RequestBody UserStatusRequest request) {
        try {
            userService.updateUserStatus(request);
            return Result.success("更新用户状态成功", null);
        } catch (Exception e) {
            log.error("更新用户状态失败", e);
            return Result.error("更新用户状态失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除用户
     */
    @DeleteMapping("/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> deleteUser(@PathVariable String userId) {
        try {
            userService.deleteUser(userId);
            return Result.success("删除用户成功", null);
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return Result.error("删除用户失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public Result<Object> getUserStatistics() {
        try {
            Object statistics = userService.getUserStatistics();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取用户统计信息失败", e);
            return Result.error("获取用户统计信息失败：" + e.getMessage());
        }
    }
}
