package com.zhisuo.manager.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 更新用户请求DTO
 */
@Data
public class UserUpdateRequest {
    
    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 密码（可选，如果不提供则不修改密码）
     */
    @Size(min = 6, max = 20, message = "密码长度必须在6-20位之间")
    private String password;
    
    /**
     * 昵称
     */
    @NotBlank(message = "昵称不能为空")
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;
    
    /**
     * 会员等级(0:普通,1:黄金,2:铂金等)
     */
    @NotNull(message = "会员等级不能为空")
    private Integer memberLevel;
    
    /**
     * 状态(1:正常,0:禁用)
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
}
