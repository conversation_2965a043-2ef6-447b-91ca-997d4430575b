package com.zhisuo.manager.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhisuo.manager.dto.UserCreateRequest;
import com.zhisuo.manager.dto.UserQueryRequest;
import com.zhisuo.manager.dto.UserStatusRequest;
import com.zhisuo.manager.dto.UserUpdateRequest;
import com.zhisuo.manager.vo.UserVO;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 分页查询用户列表
     *
     * @param request 查询条件
     * @return 用户分页列表
     */
    IPage<UserVO> getUserPage(UserQueryRequest request);
    
    /**
     * 根据用户ID获取用户详情
     *
     * @param userId 用户ID
     * @return 用户详情
     */
    UserVO getUserDetail(String userId);
    
    /**
     * 更新用户状态
     *
     * @param request 状态更新请求
     */
    void updateUserStatus(UserStatusRequest request);
    
    /**
     * 删除用户（逻辑删除）
     *
     * @param userId 用户ID
     */
    void deleteUser(String userId);
    
    /**
     * 获取用户统计信息
     *
     * @return 统计信息
     */
    Object getUserStatistics();

    /**
     * 创建新用户
     *
     * @param request 创建用户请求
     * @return 用户详情
     */
    UserVO createUser(UserCreateRequest request);

    /**
     * 更新用户信息
     *
     * @param request 更新用户请求
     * @return 用户详情
     */
    UserVO updateUser(UserUpdateRequest request);
}
