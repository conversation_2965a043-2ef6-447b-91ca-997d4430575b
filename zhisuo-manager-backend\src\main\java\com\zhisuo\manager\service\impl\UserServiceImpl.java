package com.zhisuo.manager.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhisuo.manager.dto.UserQueryRequest;
import com.zhisuo.manager.dto.UserStatusRequest;
import com.zhisuo.manager.entity.User;
import com.zhisuo.manager.mapper.UserMapper;
import com.zhisuo.manager.service.UserService;
import com.zhisuo.manager.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Override
    public IPage<UserVO> getUserPage(UserQueryRequest request) {
        Page<UserVO> page = new Page<>(request.getCurrent(), request.getSize());

        // 先查询总数
        Long total = userMapper.selectUserPageCount(request);

        // 再查询分页数据
        IPage<UserVO> result = userMapper.selectUserPage(page, request);

        // 手动设置总数
        result.setTotal(total);

        return result;
    }
    
    @Override
    public UserVO getUserDetail(String userId) {
        if (StrUtil.isBlank(userId)) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        UserVO userVO = userMapper.selectUserDetail(userId);
        if (userVO == null) {
            throw new RuntimeException("用户不存在");
        }
        
        return userVO;
    }
    
    @Override
    public void updateUserStatus(UserStatusRequest request) {
        String userId = request.getUserId();
        Integer status = request.getStatus();
        
        if (StrUtil.isBlank(userId)) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        if (status == null || (status != 0 && status != 1)) {
            throw new RuntimeException("状态值无效");
        }
        
        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 更新用户状态
        LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(User::getUserId, userId)
                .set(User::getStatus, status)
                .set(User::getUpdateTime, LocalDateTime.now());
        
        int result = userMapper.update(null, updateWrapper);
        if (result <= 0) {
            throw new RuntimeException("更新用户状态失败");
        }
        
        log.info("更新用户[{}]状态为[{}]，原因：{}", userId, status == 1 ? "正常" : "禁用", request.getReason());
    }
    
    @Override
    public void deleteUser(String userId) {
        if (StrUtil.isBlank(userId)) {
            throw new RuntimeException("用户ID不能为空");
        }
        
        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 逻辑删除（设置状态为禁用）
        LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(User::getUserId, userId)
                .set(User::getStatus, 0)
                .set(User::getUpdateTime, LocalDateTime.now());
        
        int result = userMapper.update(null, updateWrapper);
        if (result <= 0) {
            throw new RuntimeException("删除用户失败");
        }
        
        log.info("删除用户[{}]", userId);
    }
    
    @Override
    public Object getUserStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总用户数
        LambdaQueryWrapper<User> totalWrapper = new LambdaQueryWrapper<>();
        Long totalUsers = userMapper.selectCount(totalWrapper);
        statistics.put("totalUsers", totalUsers);
        
        // 正常用户数
        LambdaQueryWrapper<User> activeWrapper = new LambdaQueryWrapper<>();
        activeWrapper.eq(User::getStatus, 1);
        Long activeUsers = userMapper.selectCount(activeWrapper);
        statistics.put("activeUsers", activeUsers);
        
        // 禁用用户数
        Long disabledUsers = totalUsers - activeUsers;
        statistics.put("disabledUsers", disabledUsers);
        
        // 今日新增用户数
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime todayEnd = todayStart.plusDays(1);
        LambdaQueryWrapper<User> todayWrapper = new LambdaQueryWrapper<>();
        todayWrapper.between(User::getCreateTime, todayStart, todayEnd);
        Long todayNewUsers = userMapper.selectCount(todayWrapper);
        statistics.put("todayNewUsers", todayNewUsers);

        // 最近7天活跃用户数（基于最后登录时间）
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
        LambdaQueryWrapper<User> recentActiveWrapper = new LambdaQueryWrapper<>();
        recentActiveWrapper.eq(User::getStatus, 1)
                          .ge(User::getLastLoginTime, sevenDaysAgo);
        Long recentActiveUsers = userMapper.selectCount(recentActiveWrapper);
        statistics.put("recentActiveUsers", recentActiveUsers);
        
        // 会员等级分布
        Map<String, Long> memberLevelDistribution = new HashMap<>();
        for (int level = 0; level <= 2; level++) {
            LambdaQueryWrapper<User> levelWrapper = new LambdaQueryWrapper<>();
            levelWrapper.eq(User::getMemberLevel, level);
            Long count = userMapper.selectCount(levelWrapper);
            String levelName = level == 0 ? "普通会员" : (level == 1 ? "黄金会员" : "铂金会员");
            memberLevelDistribution.put(levelName, count);
        }
        statistics.put("memberLevelDistribution", memberLevelDistribution);

        log.info("用户统计数据: 总用户数={}, 活跃用户数={}, 今日新增={}, 最近7天活跃={}, 禁用用户数={}",
                totalUsers, activeUsers, todayNewUsers, recentActiveUsers, disabledUsers);

        return statistics;
    }
}
