<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhisuo.manager.mapper.UserMapper">

    <!-- 用户VO结果映射 -->
    <resultMap id="UserVOMap" type="com.zhisuo.manager.vo.UserVO">
        <id column="user_id" property="userId"/>
        <result column="phone" property="phone"/>
        <result column="nickname" property="nickname"/>
        <result column="avatar" property="avatar"/>
        <result column="member_level" property="memberLevel"/>
        <result column="member_level_name" property="memberLevelName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="status" property="status"/>
        <result column="status_name" property="statusName"/>
        <result column="favorite_count" property="favoriteCount"/>
        <result column="like_count" property="likeCount"/>
        <result column="comment_count" property="commentCount"/>
    </resultMap>

    <!-- 分页查询用户列表 -->
    <select id="selectUserPage" resultMap="UserVOMap">
        SELECT 
            u.user_id,
            u.phone,
            u.nickname,
            u.avatar,
            u.member_level,
            CASE u.member_level 
                WHEN 0 THEN '普通会员'
                WHEN 1 THEN '黄金会员'
                WHEN 2 THEN '铂金会员'
                ELSE '未知'
            END as member_level_name,
            u.create_time,
            u.update_time,
            u.last_login_time,
            u.status,
            CASE u.status 
                WHEN 1 THEN '正常'
                WHEN 0 THEN '禁用'
                ELSE '未知'
            END as status_name,
            COALESCE(f.favorite_count, 0) as favorite_count,
            COALESCE(l.like_count, 0) as like_count,
            COALESCE(c.comment_count, 0) as comment_count
        FROM users u
        LEFT JOIN (
            SELECT user_id, COUNT(*) as favorite_count 
            FROM user_favorites 
            GROUP BY user_id
        ) f ON u.user_id = f.user_id
        LEFT JOIN (
            SELECT user_id, COUNT(*) as like_count 
            FROM user_likes 
            GROUP BY user_id
        ) l ON u.user_id = l.user_id
        LEFT JOIN (
            SELECT user_id, COUNT(*) as comment_count 
            FROM comments 
            GROUP BY user_id
        ) c ON u.user_id = c.user_id
        <where>
            <if test="request.phone != null and request.phone != ''">
                AND u.phone LIKE CONCAT('%', #{request.phone}, '%')
            </if>
            <if test="request.nickname != null and request.nickname != ''">
                AND u.nickname LIKE CONCAT('%', #{request.nickname}, '%')
            </if>
            <if test="request.memberLevel != null">
                AND u.member_level = #{request.memberLevel}
            </if>
            <if test="request.status != null">
                AND u.status = #{request.status}
            </if>
            <if test="request.startTime != null and request.startTime != ''">
                AND u.create_time >= #{request.startTime}
            </if>
            <if test="request.endTime != null and request.endTime != ''">
                AND u.create_time &lt;= #{request.endTime}
            </if>
        </where>
        <choose>
            <when test="request.sortField == 'createTime'">
                ORDER BY u.create_time 
                <if test="request.sortOrder == 'desc'">DESC</if>
                <if test="request.sortOrder == 'asc'">ASC</if>
            </when>
            <when test="request.sortField == 'lastLoginTime'">
                ORDER BY u.last_login_time 
                <if test="request.sortOrder == 'desc'">DESC</if>
                <if test="request.sortOrder == 'asc'">ASC</if>
            </when>
            <when test="request.sortField == 'memberLevel'">
                ORDER BY u.member_level 
                <if test="request.sortOrder == 'desc'">DESC</if>
                <if test="request.sortOrder == 'asc'">ASC</if>
            </when>
            <otherwise>
                ORDER BY u.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据用户ID获取用户详情 -->
    <select id="selectUserDetail" resultMap="UserVOMap">
        SELECT 
            u.user_id,
            u.phone,
            u.nickname,
            u.avatar,
            u.member_level,
            CASE u.member_level 
                WHEN 0 THEN '普通会员'
                WHEN 1 THEN '黄金会员'
                WHEN 2 THEN '铂金会员'
                ELSE '未知'
            END as member_level_name,
            u.create_time,
            u.update_time,
            u.last_login_time,
            u.status,
            CASE u.status 
                WHEN 1 THEN '正常'
                WHEN 0 THEN '禁用'
                ELSE '未知'
            END as status_name,
            COALESCE(f.favorite_count, 0) as favorite_count,
            COALESCE(l.like_count, 0) as like_count,
            COALESCE(c.comment_count, 0) as comment_count
        FROM users u
        LEFT JOIN (
            SELECT user_id, COUNT(*) as favorite_count 
            FROM user_favorites 
            GROUP BY user_id
        ) f ON u.user_id = f.user_id
        LEFT JOIN (
            SELECT user_id, COUNT(*) as like_count 
            FROM user_likes 
            GROUP BY user_id
        ) l ON u.user_id = l.user_id
        LEFT JOIN (
            SELECT user_id, COUNT(*) as comment_count 
            FROM comments 
            GROUP BY user_id
        ) c ON u.user_id = c.user_id
        WHERE u.user_id = #{userId}
    </select>

    <!-- 分页查询用户列表的总数查询 -->
    <select id="selectUserPageCount" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT u.user_id)
        FROM users u
        <where>
            <if test="request.phone != null and request.phone != ''">
                AND u.phone LIKE CONCAT('%', #{request.phone}, '%')
            </if>
            <if test="request.nickname != null and request.nickname != ''">
                AND u.nickname LIKE CONCAT('%', #{request.nickname}, '%')
            </if>
            <if test="request.memberLevel != null">
                AND u.member_level = #{request.memberLevel}
            </if>
            <if test="request.status != null">
                AND u.status = #{request.status}
            </if>
            <if test="request.startTime != null and request.startTime != ''">
                AND u.create_time >= #{request.startTime}
            </if>
            <if test="request.endTime != null and request.endTime != ''">
                AND u.create_time &lt;= #{request.endTime}
            </if>
        </where>
    </select>

</mapper>
