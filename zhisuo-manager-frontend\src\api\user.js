import request from '@/utils/request'

export const userApi = {
  // 获取用户列表
  getUserList(params) {
    return request({
      url: '/users',
      method: 'get',
      params
    })
  },

  // 获取用户详情
  getUserDetail(userId) {
    return request({
      url: `/users/${userId}`,
      method: 'get'
    })
  },

  // 更新用户状态
  updateUserStatus(data) {
    return request({
      url: '/users/status',
      method: 'put',
      data
    })
  },

  // 删除用户
  deleteUser(userId) {
    return request({
      url: `/users/${userId}`,
      method: 'delete'
    })
  },

  // 获取用户统计信息
  getUserStatistics() {
    return request({
      url: '/users/statistics',
      method: 'get'
    })
  },

  // 创建用户
  createUser(data) {
    return request({
      url: '/users',
      method: 'post',
      data
    })
  },

  // 更新用户
  updateUser(data) {
    return request({
      url: `/users/${data.userId}`,
      method: 'put',
      data
    })
  }
}
