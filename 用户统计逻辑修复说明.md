# 用户统计逻辑修复说明

## 问题描述

在用户管理系统中发现了一个统计逻辑问题：新创建的用户会被错误地计入"最近7天活跃用户"统计中，即使他们从未登录过。

## 问题原因分析

### 1. 数据库字段设计
用户表中的 `last_login_time` 字段用于记录用户最后一次登录时间：
- 用户首次登录时会设置此字段
- 新创建但从未登录的用户此字段为 `NULL`

### 2. 原始统计逻辑
```java
// 原始代码 - 有问题的逻辑
LambdaQueryWrapper<User> recentActiveWrapper = new LambdaQueryWrapper<>();
recentActiveWrapper.eq(User::getStatus, 1)
                  .ge(User::getLastLoginTime, sevenDaysAgo);
```

### 3. 问题所在
- 查询条件 `ge(User::getLastLoginTime, sevenDaysAgo)` 只匹配 `lastLoginTime >= sevenDaysAgo` 的记录
- 对于 `lastLoginTime` 为 `NULL` 的用户，SQL 的 `>=` 比较会返回 `UNKNOWN`
- 在某些数据库配置下，`NULL` 值可能被意外包含在结果中
- 逻辑上，从未登录的用户不应该被视为"活跃用户"

## 修复方案

### 1. 修复后的统计逻辑
```java
// 修复后的代码
LambdaQueryWrapper<User> recentActiveWrapper = new LambdaQueryWrapper<>();
recentActiveWrapper.eq(User::getStatus, 1)
                  .isNotNull(User::getLastLoginTime)  // 排除从未登录的用户
                  .ge(User::getLastLoginTime, sevenDaysAgo);
```

### 2. 修复要点
- 添加 `.isNotNull(User::getLastLoginTime)` 条件
- 明确排除 `lastLoginTime` 为 `NULL` 的用户
- 确保只统计真正有登录记录的活跃用户

## 业务逻辑说明

### 1. 用户状态定义
- **新用户**: 刚创建，`lastLoginTime` 为 `NULL`
- **活跃用户**: 最近7天内有登录记录的用户
- **非活跃用户**: 超过7天未登录的用户

### 2. 统计指标含义
- **总用户数**: 所有状态为启用的用户
- **今日新增**: 今天创建的用户
- **最近7天活跃**: 最近7天内有登录记录的用户（排除从未登录的）
- **禁用用户**: 状态为禁用的用户

## 代码变更详情

### 修改文件
`zhisuo-manager-backend/src/main/java/com/zhisuo/manager/service/impl/UserServiceImpl.java`

### 变更内容
```diff
// 最近7天活跃用户数（基于最后登录时间）
LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
LambdaQueryWrapper<User> recentActiveWrapper = new LambdaQueryWrapper<>();
recentActiveWrapper.eq(User::getStatus, 1)
+                  .isNotNull(User::getLastLoginTime)  // 排除从未登录的用户
                  .ge(User::getLastLoginTime, sevenDaysAgo);
```

## 测试验证

### 1. 测试场景
1. 创建新用户（从未登录）
2. 查看统计数据中的"最近7天活跃"数量
3. 验证新用户不被计入活跃用户

### 2. 预期结果
- 新创建的用户不会影响"最近7天活跃"统计
- 只有真正登录过的用户才会被计入活跃统计
- 统计数据更加准确和有意义

## 相关影响

### 1. 前端显示
- 用户管理页面顶部的统计卡片会显示更准确的活跃用户数
- 统计数据的业务含义更加清晰

### 2. 数据一致性
- 统计逻辑与业务逻辑保持一致
- 避免了数据统计的歧义

## 扩展建议

### 1. 增加更多统计维度
```java
// 可以考虑添加以下统计
- 从未登录用户数
- 本周新增活跃用户数
- 用户活跃度分布
```

### 2. 优化用户创建流程
```java
// 在用户首次登录时设置 lastLoginTime
if (user.getLastLoginTime() == null) {
    user.setLastLoginTime(LocalDateTime.now());
    updateById(user);
}
```

## 总结

这个修复解决了一个重要的业务逻辑问题：
1. **准确性**: 统计数据更加准确，符合业务预期
2. **一致性**: 统计逻辑与业务定义保持一致
3. **可维护性**: 代码逻辑更加清晰，易于理解和维护

通过添加 `isNotNull(User::getLastLoginTime)` 条件，我们确保了"最近7天活跃用户"统计只包含真正有登录行为的用户，使得统计数据更有业务价值。
